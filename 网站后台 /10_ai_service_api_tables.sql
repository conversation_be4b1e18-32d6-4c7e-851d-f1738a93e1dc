-- AI服务API管理数据库表结构
-- 用于管理DeepSeek和豆包API密钥、模型配置等

-- AI服务提供商表
DROP TABLE IF EXISTS `ai_service_providers`;
CREATE TABLE `ai_service_providers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '服务商名称',
  `code` varchar(20) NOT NULL COMMENT '服务商代码',
  `display_name` varchar(100) NOT NULL COMMENT '显示名称',
  `api_base_url` varchar(255) NOT NULL COMMENT 'API基础URL',
  `api_version` varchar(20) DEFAULT 'v1' COMMENT 'API版本',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active' COMMENT '状态',
  `description` text COMMENT '描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI服务提供商表';

-- AI模型表
DROP TABLE IF EXISTS `ai_models`;
CREATE TABLE `ai_models` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `provider_id` int(11) NOT NULL COMMENT '服务商ID',
  `model_name` varchar(100) NOT NULL COMMENT '模型名称',
  `model_code` varchar(50) NOT NULL COMMENT '模型代码',
  `display_name` varchar(100) NOT NULL COMMENT '显示名称',
  `model_type` enum('chat','completion','embedding','image') NOT NULL DEFAULT 'chat' COMMENT '模型类型',
  `context_length` int(11) DEFAULT 4096 COMMENT '上下文长度',
  `max_tokens` int(11) DEFAULT 2048 COMMENT '最大输出tokens',
  `input_price` decimal(10,6) DEFAULT 0.000000 COMMENT '输入价格(每1M tokens)',
  `output_price` decimal(10,6) DEFAULT 0.000000 COMMENT '输出价格(每1M tokens)',
  `supports_streaming` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否支持流式输出',
  `supports_function_calling` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否支持函数调用',
  `supports_json_output` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否支持JSON输出',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active' COMMENT '状态',
  `description` text COMMENT '模型描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `provider_model` (`provider_id`, `model_code`),
  KEY `idx_provider` (`provider_id`),
  KEY `idx_status` (`status`),
  KEY `idx_type` (`model_type`),
  FOREIGN KEY (`provider_id`) REFERENCES `ai_service_providers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI模型表';

-- API密钥表
DROP TABLE IF EXISTS `ai_api_keys`;
CREATE TABLE `ai_api_keys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `provider_id` int(11) NOT NULL COMMENT '服务商ID',
  `key_name` varchar(100) NOT NULL COMMENT '密钥名称',
  `api_key` varchar(500) NOT NULL COMMENT 'API密钥(加密存储)',
  `api_secret` varchar(500) DEFAULT NULL COMMENT 'API密钥(部分服务商需要)',
  `endpoint_url` varchar(255) DEFAULT NULL COMMENT '自定义端点URL',
  `status` enum('active','inactive','invalid','expired') NOT NULL DEFAULT 'active' COMMENT '状态',
  `priority` int(11) NOT NULL DEFAULT 1 COMMENT '优先级(数字越小优先级越高)',
  `rate_limit_rpm` int(11) DEFAULT 60 COMMENT '每分钟请求限制',
  `rate_limit_tpm` int(11) DEFAULT 60000 COMMENT '每分钟token限制',
  `daily_quota` int(11) DEFAULT 0 COMMENT '每日配额(0表示无限制)',
  `used_today` int(11) NOT NULL DEFAULT 0 COMMENT '今日已使用量',
  `total_requests` int(11) NOT NULL DEFAULT 0 COMMENT '总请求数',
  `total_tokens` bigint(20) NOT NULL DEFAULT 0 COMMENT '总token数',
  `last_used_at` timestamp NULL DEFAULT NULL COMMENT '最后使用时间',
  `last_error` text COMMENT '最后错误信息',
  `last_validated_at` timestamp NULL DEFAULT NULL COMMENT '最后验证时间',
  `validation_status` enum('pending','valid','invalid','error') DEFAULT 'pending' COMMENT '验证状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_provider` (`provider_id`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`),
  KEY `idx_validation` (`validation_status`),
  FOREIGN KEY (`provider_id`) REFERENCES `ai_service_providers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI API密钥表';

-- API使用统计表
DROP TABLE IF EXISTS `ai_api_usage`;
CREATE TABLE `ai_api_usage` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `api_key_id` int(11) NOT NULL COMMENT 'API密钥ID',
  `model_id` int(11) NOT NULL COMMENT '模型ID',
  `request_id` varchar(100) DEFAULT NULL COMMENT '请求ID',
  `input_tokens` int(11) NOT NULL DEFAULT 0 COMMENT '输入token数',
  `output_tokens` int(11) NOT NULL DEFAULT 0 COMMENT '输出token数',
  `total_tokens` int(11) NOT NULL DEFAULT 0 COMMENT '总token数',
  `cost` decimal(10,6) DEFAULT 0.000000 COMMENT '费用',
  `response_time` int(11) DEFAULT NULL COMMENT '响应时间(毫秒)',
  `status` enum('success','error','timeout') NOT NULL DEFAULT 'success' COMMENT '请求状态',
  `error_message` text COMMENT '错误信息',
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_api_key` (`api_key_id`),
  KEY `idx_model` (`model_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created` (`created_at`),
  KEY `idx_user` (`user_id`),
  FOREIGN KEY (`api_key_id`) REFERENCES `ai_api_keys` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`model_id`) REFERENCES `ai_models` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI API使用统计表';

-- API配置表
DROP TABLE IF EXISTS `ai_service_config`;
CREATE TABLE `ai_service_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_type` enum('string','number','boolean','json') NOT NULL DEFAULT 'string' COMMENT '配置类型',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `is_encrypted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否加密存储',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI服务配置表';
