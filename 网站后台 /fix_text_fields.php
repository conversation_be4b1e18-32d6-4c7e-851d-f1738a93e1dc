<?php
/**
 * 修复TEXT字段类型脚本
 * 将TEXT类型改为VARCHAR以支持索引创建
 */

// 数据库配置
$db_config = [
    'host' => '127.0.0.1',
    'port' => '3306',
    'dbname' => 'xiaomeihuakefu_c',
    'username' => 'xiaomeihuakefu_c',
    'password' => '7Da5F1Xx995cxYz8',
    'charset' => 'utf8mb4'
];

function logMessage($message, $type = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    echo "[{$timestamp}] [{$type}] {$message}\n";
    flush();
}

try {
    logMessage("开始修复TEXT字段类型...");
    
    $dsn = "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['dbname']};charset={$db_config['charset']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    logMessage("数据库连接成功");
    
    // 开始事务
    $pdo->beginTransaction();
    
    try {
        // 1. 修改 license_keys 表的字段类型
        logMessage("修改 license_keys 表字段类型...");
        
        $pdo->exec("ALTER TABLE license_keys MODIFY COLUMN douyin_store_name VARCHAR(255) DEFAULT NULL");
        logMessage("✅ license_keys.douyin_store_name 修改为 VARCHAR(255)");
        
        $pdo->exec("ALTER TABLE license_keys MODIFY COLUMN douyin_store_id VARCHAR(100) DEFAULT NULL");
        logMessage("✅ license_keys.douyin_store_id 修改为 VARCHAR(100)");
        
        // 2. 修改 license_key_stores 表的字段类型
        logMessage("修改 license_key_stores 表字段类型...");
        
        $pdo->exec("ALTER TABLE license_key_stores MODIFY COLUMN douyin_store_name VARCHAR(255) DEFAULT NULL");
        logMessage("✅ license_key_stores.douyin_store_name 修改为 VARCHAR(255)");
        
        $pdo->exec("ALTER TABLE license_key_stores MODIFY COLUMN douyin_store_id VARCHAR(100) DEFAULT NULL");
        logMessage("✅ license_key_stores.douyin_store_id 修改为 VARCHAR(100)");
        
        // 3. 创建索引
        logMessage("创建索引...");
        
        // 检查索引是否已存在
        $stmt = $pdo->query("SHOW INDEX FROM license_keys WHERE Key_name = 'idx_license_keys_douyin_store_id'");
        if ($stmt->rowCount() == 0) {
            $pdo->exec("CREATE INDEX idx_license_keys_douyin_store_id ON license_keys(douyin_store_id)");
            logMessage("✅ 创建索引 idx_license_keys_douyin_store_id");
        } else {
            logMessage("⚠️ 索引 idx_license_keys_douyin_store_id 已存在");
        }
        
        $stmt = $pdo->query("SHOW INDEX FROM license_key_stores WHERE Key_name = 'idx_license_key_stores_douyin_store_id'");
        if ($stmt->rowCount() == 0) {
            $pdo->exec("CREATE INDEX idx_license_key_stores_douyin_store_id ON license_key_stores(douyin_store_id)");
            logMessage("✅ 创建索引 idx_license_key_stores_douyin_store_id");
        } else {
            logMessage("⚠️ 索引 idx_license_key_stores_douyin_store_id 已存在");
        }
        
        // 4. 更新数据
        logMessage("更新现有数据...");
        $stmt = $pdo->prepare("UPDATE license_key_stores SET store_type = 'wechat' WHERE store_type IS NULL OR store_type = ''");
        $stmt->execute();
        $affectedRows = $stmt->rowCount();
        logMessage("✅ 更新了 $affectedRows 条记录的店铺类型");
        
        // 5. 创建视图
        logMessage("创建视图...");
        
        // 先删除视图（如果存在）
        $pdo->exec("DROP VIEW IF EXISTS v_license_keys_with_stores");
        
        $createViewSQL = "
        CREATE VIEW v_license_keys_with_stores AS
        SELECT 
            lk.id,
            lk.key_value,
            lk.type,
            lk.status,
            lk.expiry_date,
            lk.has_customer_service,
            lk.has_product_listing,
            lk.is_multi_store,
            lk.created_at,
            -- 微信店铺信息
            lk.store_name as wechat_store_name,
            lk.wechat_store_id,
            -- 抖店信息
            lk.douyin_store_name,
            lk.douyin_store_id,
            -- 额外店铺数量
            (SELECT COUNT(*) FROM license_key_stores lks WHERE lks.license_key_id = lk.id) as additional_stores_count
        FROM license_keys lk";
        
        $pdo->exec($createViewSQL);
        logMessage("✅ 创建视图 v_license_keys_with_stores");
        
        // 提交事务
        $pdo->commit();
        logMessage("✅ 所有操作已提交");
        
        // 验证结果
        logMessage("验证修复结果...");
        
        $stmt = $pdo->query("SHOW COLUMNS FROM license_keys WHERE Field LIKE '%douyin%'");
        $columns = $stmt->fetchAll();
        
        foreach ($columns as $col) {
            logMessage("✅ license_keys.{$col['Field']}: {$col['Type']}");
        }
        
        $stmt = $pdo->query("SHOW COLUMNS FROM license_key_stores WHERE Field LIKE '%douyin%'");
        $columns = $stmt->fetchAll();
        
        foreach ($columns as $col) {
            logMessage("✅ license_key_stores.{$col['Field']}: {$col['Type']}");
        }
        
        $stmt = $pdo->query("SHOW INDEX FROM license_keys WHERE Key_name LIKE '%douyin%'");
        $indexes = $stmt->fetchAll();
        
        foreach ($indexes as $idx) {
            logMessage("✅ 索引: {$idx['Key_name']} 在 {$idx['Table']}.{$idx['Column_name']}");
        }
        
        $stmt = $pdo->query("SHOW INDEX FROM license_key_stores WHERE Key_name LIKE '%douyin%'");
        $indexes = $stmt->fetchAll();
        
        foreach ($indexes as $idx) {
            logMessage("✅ 索引: {$idx['Key_name']} 在 {$idx['Table']}.{$idx['Column_name']}");
        }
        
        logMessage("🎉 抖店功能修复完成！所有字段类型和索引已正确设置。", "SUCCESS");
        
    } catch (Exception $e) {
        // 回滚事务
        $pdo->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    logMessage("❌ 修复失败: " . $e->getMessage(), "ERROR");
    exit(1);
}

logMessage("✨ 修复脚本执行完成！", "SUCCESS");
?>
